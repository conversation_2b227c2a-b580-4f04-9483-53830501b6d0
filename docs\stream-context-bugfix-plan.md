# 流式内容生成器上下文丢失与消息数量异常修复方案

## 一、问题现象与根因

- **现象**：后端日志显示“原始消息个数为1，过滤后为2”，且AI生成内容上下文丢失。
- **根因**：前端未传递完整历史消息，或消息拼接逻辑有误，导致后端插入system prompt后消息数量异常，且上下文丢失。

---

## 二、修复目标

1. 保证前端每次API调用都传递完整历史消息（包括所有user/assistant/task/system消息）。
2. 前端在构建apiMessages时提前将'task'角色映射为'user'，便于调试与一致性。
3. 前端不插入system prompt，统一由后端处理。
4. 后端日志增强，输出messages内容与角色，便于排查。
5. 建议抽象统一的消息处理工具，流式与非流式共用。

---

## 三、详细修复清单

### 1. 前端修复

#### 1.1 消息历史拼接
- 【必须】每次API调用时，messages参数应包含完整的conversation.messages历史，而非仅新消息。
- 【建议】在`handleSendMessageStream`和任务执行相关API调用处，确保拼接全量历史。

#### 1.2 角色映射
- 【建议】在前端构建apiMessages时，将所有'task'角色映射为'user'，保持与后端一致。
- 参考代码：
  ```ts
  const apiMessages = messagesToSend.map(msg => ({
    role: msg.role === 'task' ? 'user' : msg.role,
    content: msg.content,
  }));
  ```

#### 1.3 system prompt插入
- 【必须】前端不插入system prompt，交由后端统一处理，避免重复。

#### 1.4 上下文窗口裁剪
- 【建议】上下文窗口裁剪逻辑（maxMessages、keepSystemMessage）与非流式版本保持一致。

#### 1.5 工具函数抽象
- 【建议】抽象统一的消息处理工具（如角色映射、裁剪、拼接），供流式与非流式共用。

---

### 2. 后端修复

#### 2.1 日志增强
- 【建议】后端日志输出messages内容与角色，便于排查前端传递内容。
- 参考代码：
  ```ts
  console.log('原始消息内容:', rawMessages.map(m => ({ role: m.role, content: m.content?.slice(0, 30) })));
  ```

#### 2.2 校验与警告
- 【建议】如messages仅有1条且无system，警告前端未传递历史。

#### 2.3 工具函数抽象
- 【建议】抽象统一的消息处理工具，流式与非流式共用，减少分支逻辑。

---

## 四、数据流与一致性流程

```mermaid
sequenceDiagram
    participant User
    participant Frontend
    participant API
    participant LLM

    User->>Frontend: 输入新消息
    Frontend->>Frontend: 拼接完整历史消息
    Frontend->>API: POST /api/chat-stream (messages: 全量历史)
    API->>API: 如无system，插入system prompt
    API->>API: 'task'角色映射为'user'
    API->>API: 过滤空内容
    API->>LLM: 调用LLM(messages)
    LLM-->>API: 返回内容
    API-->>Frontend: 返回流式内容
    Frontend->>Frontend: 更新对话历史
```

---

## 五、风险提示与注意事项

- 若前端未传递完整历史，AI生成内容将丢失上下文，影响连贯性。
- 若前后端'task'角色映射不一致，可能导致角色混乱。
- 若system prompt插入前后端重复，可能导致多条system消息。
- 修复需兼顾前后端消息格式、角色映射、历史消息拼接一致性，避免引入新上下文丢失或重复。

---

## 六、后续建议

- 统一消息处理工具函数，提升代码可维护性和一致性。
- 增加端到端测试用例，覆盖流式与非流式上下文管理场景。
- 定期回顾日志，持续监控上下文链路健康。

---