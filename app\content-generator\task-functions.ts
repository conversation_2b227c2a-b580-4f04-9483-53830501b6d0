import { Task, TaskStatus, GeneratedFile, FileStatus } from './types';

// 执行任务
export const executeTask = async (
  task: Task,
  setTasks: React.Dispatch<React.SetStateAction<Task[]>>,
  updateTodoFile: () => void,
  generateUniqueId: (prefix: string) => string,
  setConversation: any,
  conversation: any,
  setIsGenerating: React.Dispatch<React.SetStateAction<boolean>>,
  options: any,
  extractMultipleFilesFromMessage: any,
  generateDefaultFileName: any,
  setGeneratedFiles: React.Dispatch<React.SetStateAction<GeneratedFile[]>>,
  generatedFiles: GeneratedFile[]
) => {
  console.log(`开始执行任务 ${task.number}: ${task.description}`);
  
  // 更新任务状态
  setTasks(prev => prev.map(t => 
    t.id === task.id ? { ...t, status: 'in-progress' as TaskStatus } : t
  ));
  
  // 更新Todo.md文件
  updateTodoFile();
  
  // 任务消息仅显示用户输入内容，不含系统提示
  const taskPrompt = task.description;

  // 创建任务执行消息
  const taskMessage = {
    id: generateUniqueId('msg'),
    role: 'user' as const, // 兼容API
    content: taskPrompt,
    timestamp: Date.now(),
    type: 'task' as const,
    taskId: task.id
  };
  
  // 添加任务消息到对话
  setConversation((prev: any) => ({
    ...prev,
    messages: [...prev.messages, taskMessage]
  }));
  
  // 调用API执行任务
  try {
    setIsGenerating(true);
    const response = await fetch('/api/chat', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        messages: [...conversation.messages].map((msg: any) => ({
          role: msg.role,
          content: msg.content,
        })),
        model: options.model,
      }),
    });
    
    if (!response.ok) {
      throw new Error('Failed to execute task');
    }
    
    const data = await response.json();
    const aiResponseContent = data.message.content;
    
    // 添加AI回复到对话
    const messageId = `msg-${Date.now()}`;
    setConversation((prev: any) => ({
      ...prev,
      messages: [
        ...prev.messages,
        {
          id: messageId,
          role: 'assistant' as const,
          content: aiResponseContent,
          timestamp: Date.now(),
          type: 'assistant' as 'user' | 'assistant' | 'task' | 'system'
        }
      ]
    }));
    
    // 提取文件
    const extractedFiles = extractMultipleFilesFromMessage(aiResponseContent, messageId);
    
    if (extractedFiles.length > 0) {
      const newFiles: GeneratedFile[] = extractedFiles.map((file: any, index: number) => ({
        id: generateUniqueId('file'),
        name: file.filename || generateDefaultFileName(file.contentType, index),
        description: `${file.contentType === 'html' ? 'HTML' : 'Markdown'} - ${file.filename || `文件${index + 1}`}`,
        content: file.content,
        contentType: file.contentType,
        status: 'completed' as FileStatus,
        order: generatedFiles.length + index,
        viewMode: 'preview',
        timestamp: Date.now(),
      }));
      
      setGeneratedFiles(prev => [...prev, ...newFiles]);
    }
    
    // 更新任务状态
    setTasks(prev => prev.map(t => 
      t.id === task.id ? { ...t, status: 'completed' as TaskStatus, result: aiResponseContent } : t
    ));
    
    // 更新Todo.md文件
    updateTodoFile();
    
    setIsGenerating(false);
    return true;
  } catch (error) {
    console.error('Error executing task:', error);
    
    // 更新任务状态为失败
    setTasks(prev => prev.map(t => 
      t.id === task.id ? { ...t, status: 'pending' as TaskStatus } : t
    ));
    
    setIsGenerating(false);
    return false;
  }
};

// 生成总结
export const generateSummary = async (
  tasks: Task[],
  setExecutionPhase: React.Dispatch<React.SetStateAction<any>>,
  generateUniqueId: (prefix: string) => string,
  setConversation: any,
  conversation: any,
  setIsGenerating: React.Dispatch<React.SetStateAction<boolean>>,
  options: any,
  setGeneratedFiles: React.Dispatch<React.SetStateAction<GeneratedFile[]>>,
  generatedFiles: GeneratedFile[]
) => {
  console.log('开始生成总结...');
  setExecutionPhase('summarizing');

  // 构建总结提示
  const summaryPrompt = `请对以下已完成的任务进行总结：\n${tasks.map(task => `${task.number}. ${task.description}`).join('\n')}\n\n请提供一个全面的总结，包括完成了哪些任务，实现了哪些功能，以及任何需要注意的事项。\n请使用Markdown格式输出总结内容，文件名格式为"任务总结.md"。`;

  // 创建总结消息
  const summaryMessage = {
    id: generateUniqueId('msg'),
    role: 'user' as const, // 兼容API
    content: summaryPrompt,
    timestamp: Date.now(),
    type: 'task' as const
  };

  // 添加总结消息到对话
  setConversation((prev: any) => ({
    ...prev,
    messages: [...prev.messages, summaryMessage]
  }));

  // 调用API生成总结
  try {
    setIsGenerating(true);
    const response = await fetch('/api/chat', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        messages: [...conversation.messages, summaryMessage].map((msg: any) => ({
          role: msg.role,
          content: msg.content,
        })),
        model: options.model,
      }),
    });

    if (!response.ok) {
      throw new Error('Failed to generate summary');
    }

    const data = await response.json();
    const aiResponseContent = data.message.content;

    // 统一用 extractMultipleFilesFromMessage 提取文件
    // @ts-ignore
    const { extractMultipleFilesFromMessage, generateDefaultFileName } = await import('../lib/code-extractor');
    const messageId = `msg-${Date.now()}`;
    const extractedFiles = extractMultipleFilesFromMessage(aiResponseContent, messageId);

    let summaryFiles: GeneratedFile[] = [];
    if (extractedFiles.length > 0) {
      summaryFiles = extractedFiles.map((f, idx) => ({
        id: generateUniqueId('file'),
        name: f.filename || generateDefaultFileName(f.contentType, idx),
        description: 'Markdown - 任务总结',
        content: f.content,
        contentType: f.contentType,
        status: 'completed' as FileStatus,
        order: generatedFiles.length + idx,
        viewMode: 'preview',
        timestamp: Date.now(),
      }));
    } else {
      // 没有代码块，直接生成 summary.md
      summaryFiles = [{
        id: generateUniqueId('file'),
        name: generateDefaultFileName('markdown', 0),
        description: 'Markdown - 任务总结',
        content: aiResponseContent,
        contentType: 'markdown',
        status: 'completed' as FileStatus,
        order: generatedFiles.length,
        viewMode: 'preview',
        timestamp: Date.now(),
      }];
    }

    setGeneratedFiles(prev => [...prev, ...summaryFiles]);
    setExecutionPhase('completed');
    setIsGenerating(false);
    return true;
  } catch (error) {
    console.error('Error generating summary:', error);
    setIsGenerating(false);
    return false;
  }
};
