"use client";

import React, { useState, useRef, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { ContentViewerProps, ContentType, ViewMode } from './types';
import CodeDisplay from './code-display';
import HtmlPreview from './html-preview';
import MarkdownPreview from './markdown-preview';
import Toolbar from './toolbar';
import StatusBar from './status-bar';
import EditableHtmlPreview from './editable-html-preview';

const ContentViewer: React.FC<ContentViewerProps> = ({
  content: initialContent,
  contentType: initialContentType,
  initialViewMode = 'split',
  editable = false,
  theme = 'light',
  splitRatio = 50,
  hideToolbar = false,
  onChange,
  onContentTypeChange,
  onViewModeChange,
}) => {
  // 状态
  const [content, setContent] = useState(initialContent);
  const [contentType, setContentType] = useState<ContentType>(initialContentType);
  const [viewMode, setViewMode] = useState<ViewMode>(initialViewMode);
  const [currentSplitRatio, setCurrentSplitRatio] = useState(splitRatio);
  const [isDragging, setIsDragging] = useState(false);
  const [isEditing, setIsEditing] = useState(false);

  // 引用
  const containerRef = useRef<HTMLDivElement>(null);
  const dividerRef = useRef<HTMLDivElement>(null);
  const router = useRouter();

  // 处理内容变化
  const handleContentChange = (newContent: string) => {
    setContent(newContent);
    onChange?.(newContent);
  };

  // 处理内容类型变化
  const handleContentTypeChange = (type: ContentType) => {
    setContentType(type);
    onContentTypeChange?.(type);
  };

  // 处理视图模式变化
  const handleViewModeChange = (mode: ViewMode) => {
    setViewMode(mode);
    onViewModeChange?.(mode);
  };

  // 处理分割线拖动开始
  const handleDividerMouseDown = (e: React.MouseEvent) => {
    e.preventDefault();
    setIsDragging(true);
  };

  // 缓存内容分析结果
  const [parsedHtml, setParsedHtml] = useState<string>('');
  const [parsedMarkdown, setParsedMarkdown] = useState<string>('');

  // 分析内容
  useEffect(() => {
    if (contentType === 'html') {
      try {
        // 尝试解析HTML
        const parser = new DOMParser();
        const doc = parser.parseFromString(content, 'text/html');
        // 检查是否有解析错误
        const errorNode = doc.querySelector('parsererror');
        if (errorNode) {
          console.warn('HTML parsing error:', errorNode.textContent);
          // 如果解析错误，使用原始内容
          setParsedHtml(`<pre>${content}</pre>`);
        } else {
          // 使用解析后的HTML
          setParsedHtml(content);
        }
      } catch (error) {
        console.error('Error parsing HTML:', error);
        setParsedHtml(`<pre>${content}</pre>`);
      }
    } else if (contentType === 'markdown') {
      try {
        // 尝试解析Markdown
        // 这里可以使用marked或其他Markdown解析库
        // 但为了简化，我们直接使用原始内容
        setParsedMarkdown(content);
      } catch (error) {
        console.error('Error parsing Markdown:', error);
        setParsedMarkdown(content);
      }
    }
  }, [content, contentType]);

  // 确保在客户端渲染
  useEffect(() => {
    // 强制客户端渲染
    router.refresh();
  }, [router]);

  // 同步外部props.content变化到本地state
  useEffect(() => {
    setContent(initialContent);
  }, [initialContent]);

  // 处理分割线拖动
  useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      if (!isDragging || !containerRef.current) return;

      const containerRect = containerRef.current.getBoundingClientRect();
      const newRatio = ((e.clientX - containerRect.left) / containerRect.width) * 100;

      // 限制分割比例在10%到90%之间
      const clampedRatio = Math.min(Math.max(newRatio, 10), 90);
      setCurrentSplitRatio(clampedRatio);
    };

    const handleMouseUp = () => {
      setIsDragging(false);
    };

    if (isDragging) {
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
    }

    return () => {
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
    };
  }, [isDragging]);

  // 获取编辑器实例的引用
  const editorRef = useRef<any>(null);
  
  // 设置编辑器引用
  const setEditorInstance = (editor: any) => {
    editorRef.current = editor;
  };
  
  // 处理编辑切换
  const handleEditToggle = () => {
    if (isEditing) {
      // 如果当前是编辑状态，则保存并退出编辑模式
      if (editorRef.current) {
        // 从编辑器获取内容
        const html = editorRef.current.getHtml();
        const css = editorRef.current.getCss();
        
        // 合并HTML和CSS
        let fullHtml = html;
        if (css) {
          // 检查是否已有style标签
          if (fullHtml.includes('</head>')) {
            fullHtml = fullHtml.replace('</head>', `<style>${css}</style></head>`);
          } else {
            fullHtml = `<style>${css}</style>${fullHtml}`;
          }
        }
        
        // 保存内容
        handleContentChange(fullHtml);
      }
      setIsEditing(false);
    } else {
      // 如果当前不是编辑状态，则进入编辑模式
      setIsEditing(true);
      // 如果当前不是预览模式，切换到预览模式
      if (viewMode !== 'preview') {
        setViewMode('preview');
      }
    }
  };
  
  // HTML预览组件引用
  const htmlPreviewRef = useRef<any>(null);
  
  // 强制刷新HTML预览
  const refreshHtmlPreview = () => {
    console.log('Refreshing HTML preview...');
    if (htmlPreviewRef.current && typeof htmlPreviewRef.current.forceRefresh === 'function') {
      htmlPreviewRef.current.forceRefresh();
    }
  };
  
  // 渲染预览组件
  const renderPreview = () => {
    // 如果处于编辑模式，显示编辑器
    if (isEditing && contentType === 'html') {
      return (
        <EditableHtmlPreview 
          content={content} 
          onSave={(newContent) => {
            handleContentChange(newContent);
            setIsEditing(false);
          }} 
          onClose={() => setIsEditing(false)} 
          onEditorReady={setEditorInstance}
        />
      );
    }
    
    // 否则显示正常预览
    switch (contentType) {
      case 'html':
        return (
          <HtmlPreview 
            ref={htmlPreviewRef}
            content={content} 
            onChange={handleContentChange}
          />
        );
      case 'markdown':
        return <MarkdownPreview content={content} />;
      default:
        return null;
    }
  };

  // 计算行数
  const lineCount = content.split('\n').length;

  // 检测是否为slide内容
  const isSlideContent = content && (content.includes('<div class="slide"') || content.includes('class="slide'));

  return (
    <div
      ref={containerRef}
      className={`content-viewer flex flex-col h-full border border-gray-200 rounded-lg overflow-hidden ${theme === 'dark' ? 'dark' : ''}`}
      style={{ minHeight: isSlideContent ? '600px' : '400px' }}
    >
      {/* 工具栏 - 只在不隐藏时显示 */}
      {!hideToolbar && (
        <Toolbar
          contentType={contentType}
          viewMode={viewMode}
          isEditing={isEditing}
          canEdit={editable}
          onContentTypeChange={handleContentTypeChange}
          onViewModeChange={handleViewModeChange}
          onEditToggle={handleEditToggle}
        />
      )}

      {/* 内容区域 */}
      <div className="flex flex-grow relative" style={{ minHeight: isSlideContent ? '600px' : '400px', height: '100%' }}>
        {/* 代码区域 - 在代码模式或分屏模式下显示 */}
        {(viewMode === 'code' || viewMode === 'split') && (
          <div
            className="h-full"
            style={{
              width: viewMode === 'split' ? `${currentSplitRatio}%` : '100%',
              minHeight: isSlideContent ? '600px' : '400px',
              height: '100%',
              overflow: 'hidden' // 防止滚动条出现在外层
            }}
          >
            <CodeDisplay
              content={content}
              language={contentType === 'html' ? 'html' : 'markdown'}
              editable={editable}
              onChange={handleContentChange}
            />
          </div>
        )}

        {/* 分割线 - 仅在分屏模式下显示 */}
        {viewMode === 'split' && (
          <div
            ref={dividerRef}
            className="w-[5px] bg-gray-200 cursor-col-resize flex items-center justify-center hover:bg-gray-300"
            onMouseDown={handleDividerMouseDown}
          >
            <div className="w-[1px] h-6 bg-gray-400" />
          </div>
        )}

        {/* 预览区域 - 在预览模式或分屏模式下显示 */}
        {(viewMode === 'preview' || viewMode === 'split') && (
          <div
            className="overflow-auto h-full"
            style={{
              width: viewMode === 'split' ? `calc(100% - ${currentSplitRatio}% - 5px)` : '100%',
            }}
          >
            {renderPreview()}
          </div>
        )}
      </div>

      {/* 状态栏 */}
      <StatusBar
        contentType={contentType}
        lineCount={lineCount}
        encoding="UTF-8"
        onCopyCode={() => navigator.clipboard.writeText(content)}
        onRefresh={refreshHtmlPreview}
      />
    </div>
  );
};

export { ContentViewer };
export default ContentViewer;
