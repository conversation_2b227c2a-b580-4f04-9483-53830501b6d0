'use client';

import React, { useRef, useEffect, useState, forwardRef, useImperativeHandle } from 'react';
import { HtmlPreviewProps } from './types';
import EditableHtmlPreview from './editable-html-preview';

/**
 * HTML预览组件 - 使用srcdoc属性和sandbox配置来安全地渲染HTML内容
 */
const HtmlPreview = forwardRef<any, HtmlPreviewProps>(({ content, onChange }, ref) => {
  const [isEditing, setIsEditing] = useState(false);
  const iframeRef = useRef<HTMLIFrameElement>(null);
  const [refreshKey, setRefreshKey] = useState(0); // 用于强制重新渲染iframe

  // 处理HTML内容渲染
  useEffect(() => {
    // --- 修正ref清理方式 ---
    const localIframeRef = iframeRef.current;

    if (!content) {
      console.log('No content to render');
      return;
    }

    console.log(`Rendering HTML content, length: ${content.length}`);
    console.log(`Content preview: ${content.substring(0, 100)}...`);

    // 检测内容类型
    const hasJavaScript = content.includes('<script') || content.includes('javascript:');
    const hasChartJs = content.includes('Chart.js') || content.includes('chart.js') || 
                       content.includes('new Chart(') || content.includes('chart-container');
    const hasSlideContent = content.includes('<div class="slide"') || 
                           content.includes('class="slide') || 
                           content.includes('增长方法论') || 
                           content.includes('slide-');
    
    console.log('Content analysis:', { hasJavaScript, hasChartJs, hasSlideContent });

    // 处理iframe加载完成事件
    const handleIframeLoad = () => {
      if (!iframeRef.current) return;
      
      try {
        const iframe = iframeRef.current;
        const iframeWindow = iframe.contentWindow;
        const doc = iframe.contentDocument || iframeWindow?.document;
        
        if (!doc) {
          console.error('Cannot access iframe document');
          return;
        }

        console.log('Iframe loaded, applying post-processing...');
        
        // 设置iframe样式
        iframe.style.height = '100%';
        iframe.style.width = '100%';
        iframe.style.border = 'none';
        iframe.style.background = 'white';
        
        // 处理特殊内容
        if (hasJavaScript || hasChartJs) {
          enhanceJavaScriptExecution(doc);
        }
        
        if (hasSlideContent) {
          enhanceSlideVisibility(doc);
        }
        
        // 处理链接，确保在新窗口打开
        const links = doc.querySelectorAll('a');
        links.forEach(link => {
          link.target = '_blank';
          link.rel = 'noopener noreferrer';
        });
      } catch (error) {
        console.error('Error in iframe post-processing:', error);
      }
    };

    // 确保内容是完整的HTML文档
    let htmlContent = content;
    if (!content.includes('<!DOCTYPE html>') && !content.includes('<html')) {
      console.log('Content is not a complete HTML document, wrapping it');
      htmlContent = `<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <meta http-equiv="Content-Security-Policy" content="script-src 'self' 'unsafe-inline' 'unsafe-eval' https://cdn.jsdelivr.net https://cdnjs.cloudflare.com https://unpkg.com https://cdn.tailwindcss.com">
  <title>HTML Preview</title>
  <style>
    body { font-family: system-ui, sans-serif; line-height: 1.5; padding: 1rem; }
    pre { background: #f5f5f5; padding: 1rem; border-radius: 0.25rem; overflow: auto; }
    code { font-family: monospace; }
    canvas { display: block; width: 100%; height: auto; min-height: 200px; }
    .chart-container { display: block; width: 100%; height: 450px; min-height: 300px; }
    .slide { display: block; width: 100%; min-height: 600px; overflow: visible; }
  </style>
</head>
<body>
  ${content}
</body>
</html>`;
    } else {
      // 如果已经是完整HTML，确保它有适当的CSP和样式
      htmlContent = injectEnhancementsToHtml(htmlContent);
    }

    // 添加iframe加载事件监听器
    if (iframeRef.current) {
      iframeRef.current.onload = handleIframeLoad;
    }

    // 使用srcdoc属性设置内容，这比document.write更安全和可靠
    if (iframeRef.current) {
      iframeRef.current.srcdoc = htmlContent;
    }

    // 清理函数
    return () => {
      if (localIframeRef) {
        localIframeRef.onload = null;
      }
    };
  }, [content, refreshKey]);

  /**
   * 向HTML内容注入增强功能
   */
  const injectEnhancementsToHtml = (html: string): string => {
    // 添加CSP和重要样式
    const cspMeta = '<meta http-equiv="Content-Security-Policy" content="script-src \'self\' \'unsafe-inline\' \'unsafe-eval\' https://cdn.jsdelivr.net https://cdnjs.cloudflare.com https://unpkg.com https://cdn.tailwindcss.com">';
    const enhancedStyles = `
<style>
  canvas { display: block; width: 100%; height: auto; min-height: 200px; }
  .chart-container { display: block; width: 100%; height: 450px; min-height: 300px; }
  .slide { display: block; width: 100%; min-height: 600px; overflow: visible; }
</style>`;

    // 检查是否有head标签
    if (html.includes('<head>')) {
      // 在head标签后添加CSP和样式
      html = html.replace('<head>', `<head>${cspMeta}${enhancedStyles}`);
    } else if (html.includes('<html>')) {
      // 如果有html标签但没有head标签，添加head标签
      html = html.replace('<html>', `<html><head>${cspMeta}${enhancedStyles}</head>`);
    }

    // 添加脚本执行增强器
    const scriptExecutionEnhancer = `
<script>
  // 脚本执行增强器
  window.addEventListener('DOMContentLoaded', function() {
    // 确保所有canvas元素可见
    document.querySelectorAll('canvas').forEach(function(canvas) {
      canvas.style.display = 'block';
      canvas.style.visibility = 'visible';
    });
    
    // 确保所有图表容器可见
    document.querySelectorAll('.chart-container').forEach(function(container) {
      container.style.display = 'block';
      container.style.visibility = 'visible';
      container.style.height = '450px';
      container.style.minHeight = '300px';
    });
    
    // 重新执行所有内联脚本
    document.querySelectorAll('script:not([src])').forEach(function(oldScript) {
      var newScript = document.createElement('script');
      newScript.textContent = oldScript.textContent;
      oldScript.parentNode.replaceChild(newScript, oldScript);
    });
    
    console.log('Script execution enhancer completed');
  });
</script>`;

    // 在body结束标签前添加脚本执行增强器
    if (html.includes('</body>')) {
      html = html.replace('</body>', `${scriptExecutionEnhancer}</body>`);
    } else {
      // 如果没有body结束标签，添加到HTML末尾
      html += scriptExecutionEnhancer;
    }

    return html;
  };

  /**
   * 增强JavaScript执行
   */
  const enhanceJavaScriptExecution = (doc: Document) => {
    console.log('Enhancing JavaScript execution...');
    
    try {
      // 确保所有canvas元素可见
      const canvases = doc.querySelectorAll('canvas');
      if (canvases.length > 0) {
        console.log(`Found ${canvases.length} canvas elements`);
        canvases.forEach(canvas => {
          if (canvas instanceof HTMLElement) {
            canvas.style.display = 'block';
            canvas.style.visibility = 'visible';
            canvas.style.width = '100%';
            canvas.style.minHeight = '200px';
          }
        });
      }
      
      // 确保图表容器可见
      const chartContainers = doc.querySelectorAll('.chart-container');
      if (chartContainers.length > 0) {
        console.log(`Found ${chartContainers.length} chart containers`);
        chartContainers.forEach(container => {
          if (container instanceof HTMLElement) {
            container.style.display = 'block';
            container.style.visibility = 'visible';
            container.style.height = '450px';
            container.style.width = '100%';
            container.style.minHeight = '300px';
          }
        });
      }
      
      // 重新执行所有内联脚本
      const scripts = doc.querySelectorAll('script:not([src])');
      if (scripts.length > 0) {
        console.log(`Re-executing ${scripts.length} inline scripts`);
        scripts.forEach(oldScript => {
          const newScript = doc.createElement('script');
          newScript.textContent = oldScript.textContent;
          if (oldScript.parentNode) {
            oldScript.parentNode.replaceChild(newScript, oldScript);
          }
        });
      }
    } catch (err) {
      console.error('Error enhancing JavaScript execution:', err);
    }
  };

  /**
   * 增强幻灯片可见性
   */
  const enhanceSlideVisibility = (doc: Document) => {
    console.log('Enhancing slide visibility...');
    
    try {
      // 调整body样式
      if (doc.body) {
        doc.body.style.overflow = 'auto';
        doc.body.style.minHeight = '100%';
        doc.body.style.height = 'auto';
      }
      
      // 查找所有slide元素并确保它们可见
      const slides = doc.querySelectorAll('.slide, [class*="slide-"]');
      if (slides.length > 0) {
        console.log(`Found ${slides.length} slide elements`);
        slides.forEach(slide => {
          if (slide instanceof HTMLElement) {
            slide.style.display = 'block';
            slide.style.overflow = 'visible';
            slide.style.height = 'auto';
            slide.style.minHeight = '600px';
            slide.style.width = '100%';
          }
        });
      }
    } catch (err) {
      console.error('Error enhancing slide visibility:', err);
    }
  };

  // 强制重新渲染iframe
  const forceRefresh = () => {
    console.log('Forcing iframe refresh');
    setRefreshKey(prevKey => prevKey + 1);
  };
  
  // 暴露方法给父组件
  useImperativeHandle(ref, () => ({
    forceRefresh
  }));

  // 处理编辑保存
  const handleSave = (newContent: string) => {
    setIsEditing(false);
    if (onChange) {
      onChange(newContent);
    }
  };

  // 处理取消编辑
  const handleClose = () => {
    setIsEditing(false);
  };

  // 如果处于编辑模式，显示编辑器
  if (isEditing) {
    return (
      <div className="w-full h-full">
        <EditableHtmlPreview 
          content={content} 
          onSave={handleSave} 
          onClose={handleClose} 
        />
      </div>
    );
  }

  // 渲染预览
  return (
    <div className="relative w-full h-full">
      {/* iframe预览 */}
      <iframe
        ref={iframeRef}
        className="w-full h-full border-0"
        title="HTML Preview"
        sandbox="allow-scripts allow-same-origin allow-popups"
      />
    </div>
  );
});

HtmlPreview.displayName = 'HtmlPreview';
export default HtmlPreview;
