预览编辑页面bugs
1、点击工具栏编辑html的时候预览的效果显示不全
2、编辑-保存-保存为新版本新版本的代码也不完整，下面是原始版本和新版本的代码，编辑页面没有做任何改动。
原始版本：
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据趋势分析</title>
    <!-- Tailwind CSS -->
    <link href="https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/tailwindcss/2.2.19/tailwind.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-100-M/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Noto+Serif+SC:wght@400;500;600;700&family=Noto+Sans+SC:wght@300;400;500;700&display=swap" rel="stylesheet">
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        body, html {
            margin: 0;
            padding: 0;
            overflow: hidden;
        }

        .slide {
            width: 1280px;
            min-height: 720px;
            background: linear-gradient(135deg, #0f172a 0%, #1e1b4b 100%);
            color: white;
            font-family: 'Noto Sans SC', sans-serif;
            position: relative;
        }

        .gradient-text {
            background: linear-gradient(90deg, #38bdf8, #a78bfa);
            -webkit-background-clip: text;
            background-clip: text;
            color: transparent;
        }

        .glow {
            position: absolute;
            border-radius: 50%;
        }

        .glow-1 {
            width: 400px;
            height: 400px;
            bottom: -150px;
            right: -100px;
            background: radial-gradient(circle, rgba(56, 189, 248, 0.2) 0%, rgba(255, 255, 255, 0) 70%);
        }

        .glow-2 {
            width: 300px;
            height: 300px;
            top: -100px;
            left: -100px;
            background: radial-gradient(circle, rgba(167, 139, 250, 0.2) 0%, rgba(255, 255, 255, 0) 70%);
        }

        .chart-container {
            width: 100%;
            height: 400px;
        }
    </style>
</head>
<body>
    <div class="slide p-16 overflow-hidden">
        <!-- 背景元素 -->
        <div class="glow glow-1"></div>
        <div class="glow glow-2"></div>
        
        <!-- 页眉 -->
        <div class="mb-6">
            <h1 class="text-3xl font-bold mb-1 gradient-text">数据趋势分析</h1>
            <div class="w-24 h-1 bg-gradient-to-r from-blue-400 to-purple-500"></div>
        </div>
        
        <!-- 内容区域 -->
        <div class="grid grid-cols-12 gap-6">
            <div class="col-span-12">
                <div class="chart-container">
                    <canvas id="lineChart"></canvas>
                </div>
            </div>
            <div class="col-span-12 text-center">
                <p class="text-lg text-gray-300">过去一年用户增长趋势</p>
            </div>
        </div>
        
        <!-- 页脚 -->
        <div class="absolute bottom-4 right-6 text-xs text-gray-400">数据来源：内部统计 • 2023</div>
    </div>
    
    <script>
        // 图表初始化
        const ctx = document.getElementById('lineChart').getContext('2d');
        new Chart(ctx, {
            type: 'line',
            data: {
                labels: ['一月', '二月', '三月', '四月', '五月', '六月', '七月', '八月', '九月', '十月', '十一月', '十二月'],
                datasets: [{
                    label: '活跃用户数 (万)',
                    data: [65, 59, 80, 81, 56, 55, 40, 60, 75, 85, 90, 95],
                    backgroundColor: 'rgba(56, 189, 248, 0.2)',
                    borderColor: 'rgba(56, 189, 248, 1)',
                    borderWidth: 2,
                    tension: 0.1,
                    fill: true
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            color: 'rgba(255, 255, 255, 0.8)',
                            font: {
                                size: 14
                            }
                        },
                        grid: {
                            color: 'rgba(255, 255, 255, 0.1)'
                        }
                    },
                    x: {
                        ticks: {
                            color: 'rgba(255, 255, 255, 0.8)',
                            font: {
                                size: 14
                            }
                        },
                        grid: {
                            display: false
                        }
                    }
                },
                plugins: {
                    legend: {
                        labels: {
                            color: 'rgba(255, 255, 255, 0.8)',
                            font: {
                                size: 14
                            }
                        }
                    }
                }
            }
        });
    </script>
</body>
</html>


新版本
<style>* { box-sizing: border-box; } body {margin: 0;}.gjs-cv-canvas .gjs-frame{left:0px !important;top:0px !important;width:100% !important;margin-left:0px !important;}body, html{margin-top:0px;margin-right:0px;margin-bottom:0px;margin-left:0px;padding-top:0px;padding-right:0px;padding-bottom:0px;padding-left:0px;overflow-x:hidden;overflow-y:hidden;}.slide{width:1280px;min-height:720px;background-image:linear-gradient(135deg, rgb(15, 23, 42) 0%, rgb(30, 27, 75) 100%);background-position-x:initial;background-position-y:initial;background-size:initial;background-repeat:initial;background-attachment:initial;background-origin:initial;background-clip:initial;background-color:initial;color:white;font-family:"Noto Sans SC", sans-serif;position:relative;}.gradient-text{background-image:linear-gradient(90deg, rgb(56, 189, 248), rgb(167, 139, 250));background-position-x:initial;background-position-y:initial;background-size:initial;background-repeat:initial;background-attachment:initial;background-origin:initial;background-color:initial;background-clip:text;color:transparent;}.glow{position:absolute;border-top-left-radius:50%;border-top-right-radius:50%;border-bottom-right-radius:50%;border-bottom-left-radius:50%;}.glow-1{width:400px;height:400px;bottom:-150px;right:-100px;background-image:radial-gradient(circle, rgba(56, 189, 248, 0.2) 0%, rgba(255, 255, 255, 0) 70%);background-position-x:initial;background-position-y:initial;background-size:initial;background-repeat:initial;background-attachment:initial;background-origin:initial;background-clip:initial;background-color:initial;}.glow-2{width:300px;height:300px;top:-100px;left:-100px;background-image:radial-gradient(circle, rgba(167, 139, 250, 0.2) 0%, rgba(255, 255, 255, 0) 70%);background-position-x:initial;background-position-y:initial;background-size:initial;background-repeat:initial;background-attachment:initial;background-origin:initial;background-clip:initial;background-color:initial;}.chart-container{width:100%;height:400px;}</style><body><meta charset="UTF-8"/><meta name="viewport" content="width=device-width, initial-scale=1.0"/><title>数据趋势分析</title><link href="https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/tailwindcss/2.2.19/tailwind.min.css" rel="stylesheet"/><link href="https://lf6-cdn-tos.bytecdntp.com/cdn/expire-100-M/font-awesome/6.0.0/css/all.min.css" rel="stylesheet"/><link href="https://fonts.googleapis.com/css2?family=Noto+Serif+SC:wght@400;500;600;700&family=Noto+Sans+SC:wght@300;400;500;700&display=swap" rel="stylesheet"/><div class="slide p-16 overflow-hidden"><!-- 背景元素 --><div class="glow glow-1"></div><div class="glow glow-2"></div><!-- 页眉 --><div class="mb-6"><h1 class="text-3xl font-bold mb-1 gradient-text">数据趋势分析</h1><div class="w-24 h-1 bg-gradient-to-r from-blue-400 to-purple-500"></div></div><!-- 内容区域 --><div class="grid grid-cols-12 gap-6"><div class="col-span-12"><div class="chart-container"><canvas id="lineChart"></canvas></div></div><div class="col-span-12 text-center"><p class="text-lg text-gray-300">过去一年用户增长趋势</p></div></div><!-- 页脚 --><div class="absolute bottom-4 right-6 text-xs text-gray-400">数据来源：内部统计 • 2023</div></div></body>