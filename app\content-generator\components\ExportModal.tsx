import React from 'react';
import { GeneratedFile } from '../types';

/**
 * 导出选择模态框组件
 * 关注点分离：仅负责UI与交互，不包含导出实现逻辑
 * @param isOpen 是否显示模态框
 * @param onClose 关闭回调
 * @param onExport 导出回调，参数为导出类型
 * @param slideFiles 幻灯片文件列表
 */
export interface ExportModalProps {
  isOpen: boolean;
  onClose: () => void;
  onExport: (type: 'pdf' | 'ppt') => void;
  slideFiles: GeneratedFile[];
}

const ExportModal: React.FC<ExportModalProps> = ({
  isOpen,
  onClose,
  onExport,
  slideFiles,
}) => {
  if (!isOpen) return null;

  return (
    <div
      className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"
      role="dialog"
      aria-modal="true"
      aria-labelledby="export-modal-title"
    >
      <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4 shadow-lg">
        <div className="flex items-center justify-between mb-4">
          <h3
            id="export-modal-title"
            className="text-lg font-semibold text-gray-900"
          >
            导出幻灯片
          </h3>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600"
            aria-label="关闭导出模态框"
          >
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        <div className="mb-4">
          <p className="text-gray-600 mb-3">
            检测到 {slideFiles.length} 个幻灯片文件，请选择导出格式：
          </p>
          <div className="space-y-2 text-sm text-gray-500 max-h-32 overflow-y-auto">
            {slideFiles.map((file) => (
              <div key={file.id} className="flex items-center">
                <svg className="w-4 h-4 mr-2 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
                <span>{file.name}</span>
              </div>
            ))}
          </div>
        </div>

        <div className="flex flex-col gap-3">
          <button
            onClick={() => onExport('pdf')}
            className="flex items-center justify-center px-4 py-3 bg-red-500 text-white rounded-lg hover:bg-red-600 transition-colors"
            aria-label="导出为PDF"
          >
            <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
            导出为 PDF
          </button>
          <button
            onClick={() => onExport('ppt')}
            className="flex items-center justify-center px-4 py-3 bg-orange-500 text-white rounded-lg hover:bg-orange-600 transition-colors"
            aria-label="导出为PPT"
          >
            <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 17v-2m3 2v-4m3 4v-6m2 10H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
            导出为 PPT 格式
          </button>
        </div>

        <div className="mt-4 text-xs text-gray-500">
          <p>• PDF：将保持原有样式和排版</p>
          <p>• PPT：生成PowerPoint兼容的HTML文件</p>
        </div>
      </div>
    </div>
  );
};

export default ExportModal;