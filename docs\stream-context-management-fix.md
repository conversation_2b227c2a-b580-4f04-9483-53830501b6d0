# 流式内容生成器上下文管理修复报告

## 问题分析

### 核心问题
流式版本 (`content-generator-stream.tsx`) 存在严重的上下文管理问题，导致任务执行过程中出现幻觉和死循环。

### 问题根源

#### 1. 任务规划逻辑过于复杂
**流式版本（有问题）**：
- 在每次AI回复完成后都会调用 `shouldPerformTaskPlanning` 进行复杂判断
- 任务执行期间的AI回复也会触发任务规划判断，容易产生误判
- 复杂的任务规划判断逻辑容易出错

**非流式版本（稳定）**：
- 只在用户发送消息后的 `handleSendMessage` 中进行任务提取
- 任务执行期间不会触发新的任务规划
- 使用独立的 `executeTask` 函数，逻辑简单清晰

#### 2. 上下文管理过于复杂
**流式版本（有问题）**：
- 复杂的上下文过滤和处理逻辑
- 多层嵌套的消息处理
- 容易丢失关键上下文信息

**非流式版本（稳定）**：
- 直接传递完整的 `conversation.messages`
- 简单的上下文窗口管理
- 稳定的消息传递机制

## 修复方案

### 1. 简化任务规划逻辑
```tsx
// 修复前：复杂的任务规划判断
const shouldExtractTasks = shouldPerformTaskPlanning(userMessage.content, finalContent);
if (shouldExtractTasks) {
  // 复杂的判断逻辑...
}

// 修复后：简化为直接任务提取
const extractedTasks = extractTasksFromResponse(finalContent);
if (extractedTasks.length > 0) {
  // 直接处理任务...
}
```

### 2. 简化上下文管理
```tsx
// 修复前：复杂的上下文过滤
if (contextOptions.maxMessages > 0 && messagesToSend.length > contextOptions.maxMessages) {
  const systemMessages = contextOptions.keepSystemMessage ? ... : [];
  const recentMessages = messagesToSend.filter(...).slice(-contextOptions.maxMessages);
  messagesToSend = [...systemMessages, ...recentMessages];
}

// 修复后：直接使用完整对话历史
const messagesToSend = [...conversation.messages];
```

### 3. 简化消息构建
```tsx
// 修复前：复杂的消息构建逻辑
const apiMessages = [
  ...messagesToSend
    .filter(msg => msg.id !== userFriendlyTaskMessage.id)
    .map(msg => ({ role: msg.role, content: msg.content })),
  { role: taskMessage.role, content: taskMessage.content }
];

// 修复后：参考非流式版本的简单方式
const apiMessages = [...messagesToSend, taskMessage].map((msg: any) => ({
  role: msg.role,
  content: msg.content,
}));
```

## 修复内容

### 1. 删除复杂的任务规划判断函数
- 移除 `shouldPerformTaskPlanning` 函数
- 简化为直接调用 `extractTasksFromResponse`

### 2. 简化上下文管理
- 移除复杂的上下文窗口处理逻辑
- 直接使用完整的对话历史

### 3. 清理不必要的变量
- 移除 `previousTasksContext`
- 移除 `generatedFilesContext`
- 移除 `relatedTasks`

### 4. 修复依赖项
- 更新 useCallback 的依赖项数组
- 移除不再使用的依赖项

## 预期效果

### 1. 解决死循环问题
- 任务执行期间不再触发新的任务规划
- 避免子任务重新进行任务规划

### 2. 提高稳定性
- 简化的上下文管理减少出错概率
- 与非流式版本保持一致的逻辑

### 3. 改善性能
- 减少不必要的计算和判断
- 简化的消息处理逻辑

## 测试建议

1. **基本功能测试**：
   - 测试任务规划和执行是否正常
   - 验证Todo.md文件是否正确创建和更新

2. **死循环测试**：
   - 测试任务执行过程中是否还会出现死循环
   - 验证子任务是否正确执行而不是重新规划

3. **上下文一致性测试**：
   - 测试任务执行过程中上下文是否保持一致
   - 验证生成的内容是否符合预期

4. **对比测试**：
   - 与非流式版本进行对比测试
   - 确保行为一致性

## 总结

通过参考非流式版本的稳定机制，简化了流式版本的任务规划和上下文管理逻辑，预期能够解决死循环和幻觉问题，提高系统的稳定性和可靠性。
