# ExportButton 组件重构任务文档

## 一、背景与目标

当前 `app/content-generator/components/export-button.tsx` 组件集成了导出按钮、导出模态框、PDF/PPT导出、HTML生成等多重职责，导致代码体量大、关注点分散、维护和扩展难度高。为提升可维护性、可扩展性、类型安全和用户体验，需对其进行系统性重构。

---

## 二、主要问题清单

- **关注点分离不足**：UI组件与导出逻辑混杂，难以单元测试和维护。
- **代码复用性差**：PDF/PPT/HTML导出存在大量重复逻辑，未抽象为工具函数。
- **类型与健壮性不足**：部分异步操作和DOM解析缺乏异常处理，类型约束不够严格。
- **UI可访问性有待提升**：模态框无aria属性，错误提示方式不友好。
- **扩展性与性能风险**：所有导出逻辑均在客户端执行，未来扩展新格式或大文件导出时风险较高。

---

## 三、重构目标

1. **关注点彻底分离**：UI组件与导出逻辑解耦，便于维护和测试。
2. **提升代码复用性**：抽象资源收集、HTML拼接等为独立工具函数。
3. **增强类型安全与健壮性**：完善类型定义，所有异步操作加异常处理，错误通过全局Toast反馈。
4. **优化UI可访问性**：模态框增加aria属性，按钮/模态框样式与全局UI风格统一。
5. **提升扩展性与性能**：为未来支持更多导出格式和服务端导出预留接口。

---

## 四、重构分步计划

### 步骤1：UI组件拆分

- `ExportButton.tsx` 仅负责按钮与状态管理。
- 新建 `ExportModal.tsx`，仅负责模态框UI与交互。
- 通过props和回调解耦两者。

### 步骤2：导出逻辑抽象

- 新建 `/lib/export-utils.ts`，封装如下工具函数：
  - `collectResourcesFromHTML(files: GeneratedFile[]): { styles, links, scripts, externalScripts }`
  - `generateExportHTML(files: GeneratedFile[], type: 'pdf' | 'ppt'): string`
  - `downloadFile(blob: Blob, filename: string): void`
- `exportToPDF`、`exportToPPT` 仅负责调度与下载。

### 步骤3：类型与错误处理优化

- 所有异步操作加try/catch，错误通过全局Toast反馈。
- 增加类型注释与JSDoc，提升类型安全。

### 步骤4：可访问性与UI一致性

- 模态框增加aria属性，提升可访问性。
- 按钮/模态框样式与全局UI风格统一。

### 步骤5：扩展性与性能提升

- 评估将导出逻辑迁移到API（如 `/api/export`），客户端仅负责触发与反馈。
- 设计导出策略模式，便于未来扩展更多导出格式。

---

## 五、架构可视化

```mermaid
flowchart TD
    A[ExportButton UI组件] -->|点击| B(ExportModal UI组件)
    B -->|选择格式| C{导出类型}
    C -- PDF --> D1[exportToPDF 工具函数]
    C -- PPT --> D2[exportToPPT 工具函数]
    D1 & D2 --> E[exportUtils: 资源收集/HTML生成]
    E --> F[API或本地下载]
```

---

## 六、变更影响与风险

- **影响文件**：`export-button.tsx`、新建 `export-modal.tsx`、新建 `/lib/export-utils.ts`
- **需回归测试**：导出功能、UI交互、异常处理
- **风险点**：重构后需确保所有导出场景兼容，避免XSS与内容丢失

---

## 七、重构任务清单（TodoList）

- [ ] 拆分UI组件
- [ ] 抽象导出工具函数
- [ ] 优化类型与错误处理
- [ ] 增强可访问性
- [ ] 设计导出策略模式
- [ ] 回归测试与文档补充

---

## 八、后续行动

1. 按上述方案进行重构开发。
2. 完成后进行功能回归测试与代码审查。
3. 更新相关文档与开发规范。

---

> 本文档为 ExportButton 组件重构的详细任务说明，后续如有需求变更请及时同步。