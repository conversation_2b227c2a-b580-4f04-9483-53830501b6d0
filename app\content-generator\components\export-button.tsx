"use client";

import React, { useState } from 'react';
import { GeneratedFile } from '../types';
import ExportModal from './ExportModal';
import { isSlideContent, generatePDFHTML, generatePPTHTML as importedGeneratePPTHTML } from '../../../lib/export-utils';

interface ExportButtonProps {
  files: GeneratedFile[];
  disabled?: boolean;
}


// 检测是否为slide内容

// 导出选择模态框

const ExportButton: React.FC<ExportButtonProps> = ({ files, disabled = false }) => {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isExporting, setIsExporting] = useState(false);

  // 筛选出slide类型的文件
  const slideFiles = files.filter(file =>
    file.status === 'completed' &&
    file.contentType === 'html' &&
    isSlideContent(file.content)
  );

  // 如果没有slide文件，不显示导出按钮
  if (slideFiles.length === 0) {
    return null;
  }

  const handleExport = async (type: 'pdf' | 'ppt') => {
    setIsExporting(true);
    setIsModalOpen(false);

    try {
      if (type === 'pdf') {
        await exportToPDF(slideFiles);
      } else {
        await exportToPPT(slideFiles);
      }
    } catch (error) {
      console.error('导出失败:', error);
      alert('导出失败，请查看控制台获取详细信息');
    } finally {
      setIsExporting(false);
    }
  };

  return (
    <>
      <button
        onClick={() => setIsModalOpen(true)}
        disabled={disabled || isExporting}
        className="px-3 py-1.5 bg-gradient-to-r from-purple-500 to-purple-600 text-white rounded-lg hover:shadow-md transition-all duration-200 text-sm flex items-center disabled:opacity-50 disabled:cursor-not-allowed"
      >
        {isExporting ? (
          <>
            <svg className="animate-spin h-4 w-4 mr-1.5" fill="none" viewBox="0 0 24 24">
              <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
              <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            导出中...
          </>
        ) : (
          <>
            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
            导出
          </>
        )}
      </button>

      <ExportModal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        onExport={handleExport}
        slideFiles={slideFiles}
      />
    </>
  );
};

// PDF导出功能 - 使用Puppeteer API
const exportToPDF = async (slideFiles: GeneratedFile[]) => {
  try {
    console.log('开始PDF导出，文件数量:', slideFiles.length);

    if (slideFiles.length === 1) {
      // 单个文件导出
      const file = slideFiles[0];
      const filename = file.name.replace('.html', '.pdf');

      console.log('单文件导出:', filename);

      const response = await fetch('/api/generate-pdf', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          htmlContent: file.content,
          filename: filename,
          options: {
            format: 'A4',
            landscape: true,
            printBackground: true,
            margin: {
              top: '5mm',
              right: '5mm',
              bottom: '5mm',
              left: '5mm'
            }
          }
        })
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'PDF生成失败');
      }

      // 下载PDF文件
      const blob = await response.blob();
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = filename;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);

      console.log('单文件PDF导出成功');
    } else {
      // 多个文件合并导出
      const filename = `slides-presentation-${new Date().toISOString().slice(0, 10)}.pdf`;

      console.log('多文件导出:', filename, '文件数量:', slideFiles.length);

      // 使用PPT导出的HTML合并逻辑生成完整的HTML
      const combinedHTML = generatePDFHTML(slideFiles);

      const response = await fetch('/api/generate-pdf', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          htmlContent: combinedHTML,
          filename: filename,
          options: {
            format: 'A4',
            landscape: true,
            printBackground: true,
            margin: {
              top: '5mm',
              right: '5mm',
              bottom: '5mm',
              left: '5mm'
            }
          }
        })
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || '多文件PDF生成失败');
      }

      // 下载PDF文件
      const blob = await response.blob();
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = filename;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);

      console.log('多文件PDF导出成功');
    }
  } catch (error) {
    console.error('PDF导出错误:', error);
    throw new Error(`PDF导出失败: ${error instanceof Error ? error.message : '未知错误'}`);
  }
};

// PPT导出功能（生成PowerPoint兼容的HTML）
const exportToPPT = async (slideFiles: GeneratedFile[]) => {
  try {
    console.log('Starting PPT export process...');

    // 收集所有样式、外部链接和脚本
    const allStyles = new Set<string>();
    const allLinks = new Set<string>();
    const allScripts = new Set<string>();
    const allExternalScripts = new Set<string>();

    // 处理每个幻灯片文件
    const slideContents = slideFiles.map((file, index) => {
      let slideContent = '';

      try {
        // 如果是完整的HTML文档，提取内容和样式
        if (file.content.includes('<!DOCTYPE') || file.content.includes('<html')) {
          const parser = new DOMParser();
          const doc = parser.parseFromString(file.content, 'text/html');

          // 收集样式
          const styles = doc.head?.querySelectorAll('style');
          styles?.forEach(style => {
            if (style.textContent) {
              allStyles.add(style.textContent);
            }
          });

          // 收集外部链接
          const links = doc.head?.querySelectorAll('link[rel="stylesheet"]');
          links?.forEach(link => {
            const href = link.getAttribute('href');
            if (href) {
              allLinks.add(href);
            }
          });

          // 收集内联脚本
          const scripts = doc.querySelectorAll('script:not([src])');
          scripts.forEach(script => {
            if (script.textContent) {
              allScripts.add(script.textContent);
            }
          });

          // 收集外部脚本
          const externalScripts = doc.querySelectorAll('script[src]');
          externalScripts.forEach(script => {
            const src = script.getAttribute('src');
            if (src) {
              allExternalScripts.add(src);
            }
          });

          // 直接使用完整的body内容
          slideContent = doc.body?.innerHTML || file.content;
        } else {
          slideContent = file.content;
        }
      } catch (error) {
        console.error(`Error processing slide ${index + 1}:`, error);
        slideContent = file.content; // 出错时使用原始内容
      }

      return `
      <div class="slide-page" data-slide="${index + 1}" id="slide-${index + 1}">
        ${slideContent}
      </div>`;
    }).join('\n');

    // 处理收集到的资源
    const combinedStyles = Array.from(allStyles).join('\n');
    const linkTags = Array.from(allLinks).map(href => `<link href="${href}" rel="stylesheet">`).join('\n    ');
    const scriptTags = Array.from(allExternalScripts).map(src => `<script src="${src}"></script>`).join('\n    ');
    const inlineScriptsContent = Array.from(allScripts).join('\n');

    // 创建PowerPoint兼容的HTML结构
    const pptHtml = `<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>演示文稿 - ${slideFiles.length} 页</title>
    ${linkTags}
    ${scriptTags}
    <style>
        body, html {
            margin: 0;
            padding: 0;
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: white;
            scroll-behavior: smooth;
        }

        .presentation-container {
            max-width: 1280px;
            margin: 0 auto;
            padding: 20px;
            background: white !important;
        }

        .slide-page {
            width: 1280px;
            min-height: 720px;
            margin: 20px auto;
            background: white !important;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
            page-break-after: always;
            position: relative;
            border-radius: 8px;
            overflow: hidden;
        }

        .slide-page .slide {
            width: 1280px;
            height: 720px;
            margin: 0;
            border-radius: 0;
            position: relative;
        }

        .navigation {
            position: fixed;
            top: 20px;
            right: 20px;
            background: rgba(0,0,0,0.8);
            color: white;
            padding: 15px;
            border-radius: 8px;
            z-index: 1000;
            display: flex;
            flex-direction: column;
            gap: 10px;
        }

        .nav-button {
            background: rgba(255,255,255,0.2);
            border: none;
            color: white;
            padding: 8px 12px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
            transition: background 0.3s;
        }

        .nav-button:hover {
            background: rgba(255,255,255,0.3);
        }

        .slide-counter {
            position: fixed;
            bottom: 20px;
            right: 20px;
            background: rgba(0,0,0,0.8);
            color: white;
            padding: 8px 15px;
            border-radius: 5px;
            font-size: 14px;
            z-index: 1000;
        }

        .slide-navigation {
            position: fixed;
            bottom: 20px;
            left: 50%;
            transform: translateX(-50%);
            background: rgba(0,0,0,0.8);
            color: white;
            padding: 10px 20px;
            border-radius: 25px;
            z-index: 1000;
            display: flex;
            gap: 10px;
            align-items: center;
        }

        .slide-nav-button {
            background: rgba(255,255,255,0.2);
            border: none;
            color: white;
            padding: 8px 12px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
            transition: background 0.3s;
        }

        .slide-nav-button:hover {
            background: rgba(255,255,255,0.3);
        }

        .slide-nav-button:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        @media print {
            .navigation, .slide-counter, .slide-navigation { display: none; }
            .slide-page { margin: 0; box-shadow: none; page-break-after: always; }
        }

        /* 原始幻灯片样式 */
        ${combinedStyles}

        /* 强制覆盖容器背景色 - 必须在原始样式之后 */
        body, html {
            background: white !important;
        }
        .presentation-container {
            background: white !important;
        }
        .slide-page {
            background: white !important;
        }

        /* 确保图表和canvas元素可见 */
        canvas { display: block !important; width: 100% !important; height: auto !important; min-height: 200px !important; visibility: visible !important; }
        .chart-container { display: block !important; width: 100% !important; height: 450px !important; min-height: 300px !important; visibility: visible !important; }
        .slide { display: block !important; width: 100% !important; min-height: 600px !important; overflow: visible !important; }
    </style>
</head>
<body>
    <div class="navigation">
        <button class="nav-button" onclick="window.print()">打印/保存为PDF</button>
        <button class="nav-button" onclick="scrollToTop()">回到顶部</button>
        <button class="nav-button" onclick="toggleFullscreen()">全屏模式</button>
    </div>

    <div class="slide-counter">
        <span id="current-slide">1</span> / ${slideFiles.length}
    </div>

    <div class="slide-navigation">
        <button class="slide-nav-button" onclick="previousSlide()" id="prev-btn">上一页</button>
        <span id="slide-indicator">第 1 页</span>
        <button class="slide-nav-button" onclick="nextSlide()" id="next-btn">下一页</button>
    </div>

    <div class="presentation-container" id="presentation">
        ${slideContents}
    </div>

    <script>
        // 增强JavaScript执行函数
        function enhanceJavaScriptExecution() {
            console.log('Enhancing JavaScript execution in exported PPT...');

            // 确保所有canvas元素可见
            document.querySelectorAll('canvas').forEach(function(canvas) {
                canvas.style.display = 'block';
                canvas.style.visibility = 'visible';
                canvas.style.width = '100%';
                canvas.style.minHeight = '200px';
            });

            // 确保图表容器可见
            document.querySelectorAll('.chart-container').forEach(function(container) {
                container.style.display = 'block';
                container.style.visibility = 'visible';
                container.style.height = '450px';
                container.style.width = '100%';
                container.style.minHeight = '300px';
            });

            // 确保幻灯片元素可见
            document.querySelectorAll('.slide, [class*="slide-"]').forEach(function(slide) {
                slide.style.display = 'block';
                slide.style.overflow = 'visible';
                slide.style.height = 'auto';
                slide.style.minHeight = '600px';
                slide.style.width = '100%';
            });
        }

        // 在页面加载完成后执行增强功能
        window.addEventListener('DOMContentLoaded', function() {
            try {
                // 执行所有收集到的内联脚本
                ${inlineScriptsContent ? inlineScriptsContent : '// No inline scripts to execute'}

                // 重新执行所有内联脚本以确保动态内容正确渲染
                document.querySelectorAll('script:not([src])').forEach(function(oldScript) {
                    try {
                        var newScript = document.createElement('script');
                        newScript.textContent = oldScript.textContent;
                        if (oldScript.parentNode) {
                            oldScript.parentNode.replaceChild(newScript, oldScript);
                        }
                    } catch (err) {
                        console.error('Error re-executing script:', err);
                    }
                });

                // 执行增强功能
                enhanceJavaScriptExecution();
            } catch (err) {
                console.error('Error in DOMContentLoaded handler:', err);
            }
        });

        let currentSlideIndex = 0;
        const totalSlides = ${slideFiles.length};

        // 更新幻灯片指示器
        function updateSlideIndicator() {
            document.getElementById('current-slide').textContent = currentSlideIndex + 1;
            document.getElementById('slide-indicator').textContent = \`第 \${currentSlideIndex + 1} 页\`;

            // 更新按钮状态
            document.getElementById('prev-btn').disabled = currentSlideIndex === 0;
            document.getElementById('next-btn').disabled = currentSlideIndex === totalSlides - 1;
        }

        // 滚动到指定幻灯片
        function scrollToSlide(index) {
            if (index >= 0 && index < totalSlides) {
                currentSlideIndex = index;
                const slide = document.getElementById(\`slide-\${index + 1}\`);
                if (slide) {
                    slide.scrollIntoView({ behavior: 'smooth', block: 'start' });
                }
                updateSlideIndicator();
            }
        }

        // 上一页
        function previousSlide() {
            if (currentSlideIndex > 0) {
                scrollToSlide(currentSlideIndex - 1);
            }
        }

        // 下一页
        function nextSlide() {
            if (currentSlideIndex < totalSlides - 1) {
                scrollToSlide(currentSlideIndex + 1);
            }
        }

        // 回到顶部
        function scrollToTop() {
            scrollToSlide(0);
        }

        // 全屏模式
        function toggleFullscreen() {
            if (!document.fullscreenElement) {
                document.documentElement.requestFullscreen();
            } else {
                document.exitFullscreen();
            }
        }

        // 键盘导航支持
        document.addEventListener('keydown', function(e) {
            switch(e.key) {
                case 'ArrowDown':
                case 'PageDown':
                case ' ': // 空格键
                    e.preventDefault();
                    nextSlide();
                    break;
                case 'ArrowUp':
                case 'PageUp':
                    e.preventDefault();
                    previousSlide();
                    break;
                case 'Home':
                    e.preventDefault();
                    scrollToSlide(0);
                    break;
                case 'End':
                    e.preventDefault();
                    scrollToSlide(totalSlides - 1);
                    break;
                case 'F11':
                    e.preventDefault();
                    toggleFullscreen();
                    break;
            }
        });

        // 鼠标滚轮支持
        let isScrolling = false;
        document.addEventListener('wheel', function(e) {
            if (isScrolling) return;

            isScrolling = true;
            setTimeout(() => { isScrolling = false; }, 300);

            if (e.deltaY > 0) {
                // 向下滚动
                nextSlide();
            } else {
                // 向上滚动
                previousSlide();
            }
        }, { passive: true });

        // 触摸支持（移动端）
        let touchStartY = 0;
        document.addEventListener('touchstart', function(e) {
            touchStartY = e.touches[0].clientY;
        });

        document.addEventListener('touchend', function(e) {
            const touchEndY = e.changedTouches[0].clientY;
            const diff = touchStartY - touchEndY;

            if (Math.abs(diff) > 50) { // 最小滑动距离
                if (diff > 0) {
                    nextSlide();
                } else {
                    previousSlide();
                }
            }
        });

        // 监听滚动事件，更新当前幻灯片指示器
        let scrollTimeout;
        window.addEventListener('scroll', function() {
            clearTimeout(scrollTimeout);
            scrollTimeout = setTimeout(() => {
                const slides = document.querySelectorAll('.slide-page');
                const viewportHeight = window.innerHeight;
                const scrollTop = window.scrollY;

                for (let i = 0; i < slides.length; i++) {
                    const slide = slides[i];
                    const rect = slide.getBoundingClientRect();

                    // 如果幻灯片在视口中心附近
                    if (rect.top <= viewportHeight / 2 && rect.bottom >= viewportHeight / 2) {
                        if (currentSlideIndex !== i) {
                            currentSlideIndex = i;
                            updateSlideIndicator();
                        }
                        break;
                    }
                }
            }, 100);
        });

        // 初始化
        updateSlideIndicator();
    </script>
</body>
</html>`;

    // 创建Blob并触发下载
    const blob = new Blob([pptHtml], { type: 'text/html' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `slides-presentation-${new Date().toISOString().slice(0, 10)}.html`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);

    console.log('PPT export completed successfully');
  } catch (error) {
    console.error('Error during PPT export:', error);
    throw error; // 向上抛出错误以便外部函数处理
  }
};

// 注意：以下函数已被新的Puppeteer API替代，保留用于PPT导出功能

// 生成PowerPoint兼容的HTML
const generatePPTHTML = (slideFiles: GeneratedFile[]): string => {
  // 收集所有样式、外部链接和脚本
  const allStyles = new Set<string>();
  const allLinks = new Set<string>();
  const allScripts = new Set<string>(); // 收集所有脚本内容
  const allExternalScripts = new Set<string>(); // 收集所有外部脚本链接

  // 处理每个幻灯片文件
  const slideContents = slideFiles.map((file, index) => {
    let slideContent = '';

    try {
      // 如果是完整的HTML文档，提取内容和样式
      if (file.content.includes('<!DOCTYPE') || file.content.includes('<html')) {
        const parser = new DOMParser();
        const doc = parser.parseFromString(file.content, 'text/html');

        // 收集样式
        const styles = doc.head?.querySelectorAll('style');
        styles?.forEach(style => {
          if (style.textContent) {
            allStyles.add(style.textContent);
          }
        });

        // 收集外部链接
        const links = doc.head?.querySelectorAll('link[rel="stylesheet"]');
        links?.forEach(link => {
          const href = link.getAttribute('href');
          if (href) {
            // 确保外部链接是绝对路径
            if (href.startsWith('http') || href.startsWith('//')) {
              allLinks.add(href);
            } else {
              // 如果是相对路径，尝试转换为绝对URL
              try {
                const absoluteUrl = new URL(href, window.location.href).href;
                allLinks.add(absoluteUrl);
              } catch (e) {
                console.warn('Failed to convert relative URL to absolute:', href);
                allLinks.add(href); // 仍然添加原始链接
              }
            }
          }
        });

        // 收集内联脚本
        const scripts = doc.querySelectorAll('script:not([src])');
        scripts.forEach(script => {
          if (script.textContent) {
            allScripts.add(script.textContent);
          }
        });

        // 收集外部脚本
        const externalScripts = doc.querySelectorAll('script[src]');
        externalScripts.forEach(script => {
          const src = script.getAttribute('src');
          if (src) {
            // 确保外部脚本是绝对路径
            if (src.startsWith('http') || src.startsWith('//')) {
              allExternalScripts.add(src);
            } else {
              // 如果是相对路径，尝试转换为绝对URL
              try {
                const absoluteUrl = new URL(src, window.location.href).href;
                allExternalScripts.add(absoluteUrl);
              } catch (e) {
                console.warn('Failed to convert relative script URL to absolute:', src);
                allExternalScripts.add(src); // 仍然添加原始链接
              }
            }
          }
        });

        // 直接使用完整的body内容，不要只提取slide
        slideContent = doc.body?.innerHTML || file.content;
      } else {
        slideContent = file.content;
      }
    } catch (error) {
      console.error(`Error processing slide ${index + 1}:`, error);
      slideContent = file.content; // 出错时使用原始内容
    }

    return `
    <div class="slide-page" data-slide="${index + 1}" id="slide-${index + 1}">
      ${slideContent}
    </div>`;
  }).join('\n');

  // 合并所有样式
  const combinedStyles = Array.from(allStyles).join('\n');

  // 生成外部链接标签
  const linkTags = Array.from(allLinks).map(href =>
    `<link href="${href}" rel="stylesheet">`
  ).join('\n    ');

  return `<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>演示文稿 - ${slideFiles.length} 页</title>
    ${linkTags}
    <style>
        body, html {
            margin: 0;
            padding: 0;
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: white;
            scroll-behavior: smooth;
        }

        .presentation-container {
            max-width: 1280px;
            margin: 0 auto;
            padding: 20px;
            background: white !important;
        }

        .slide-page {
            width: 1280px;
            min-height: 720px;
            margin: 20px auto;
            background: white !important;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
            page-break-after: always;
            position: relative;
            border-radius: 8px;
            overflow: hidden;
        }

        .slide-page .slide {
            width: 1280px;
            height: 720px;
            margin: 0;
            border-radius: 0;
            position: relative;
        }

        .navigation {
            position: fixed;
            top: 20px;
            right: 20px;
            background: rgba(0,0,0,0.8);
            color: white;
            padding: 15px;
            border-radius: 8px;
            z-index: 1000;
            display: flex;
            flex-direction: column;
            gap: 10px;
        }

        .nav-button {
            background: rgba(255,255,255,0.2);
            border: none;
            color: white;
            padding: 8px 12px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
            transition: background 0.3s;
        }

        .nav-button:hover {
            background: rgba(255,255,255,0.3);
        }

        .slide-counter {
            position: fixed;
            bottom: 20px;
            right: 20px;
            background: rgba(0,0,0,0.8);
            color: white;
            padding: 8px 15px;
            border-radius: 5px;
            font-size: 14px;
            z-index: 1000;
        }

        .slide-navigation {
            position: fixed;
            bottom: 20px;
            left: 50%;
            transform: translateX(-50%);
            background: rgba(0,0,0,0.8);
            color: white;
            padding: 10px 20px;
            border-radius: 25px;
            z-index: 1000;
            display: flex;
            gap: 10px;
            align-items: center;
        }

        .slide-nav-button {
            background: rgba(255,255,255,0.2);
            border: none;
            color: white;
            padding: 8px 12px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
            transition: background 0.3s;
        }

        .slide-nav-button:hover {
            background: rgba(255,255,255,0.3);
        }

        .slide-nav-button:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        @media print {
            .navigation, .slide-counter, .slide-navigation { display: none; }
            .slide-page { margin: 0; box-shadow: none; page-break-after: always; }
        }

        /* 原始幻灯片样式 */
        ${combinedStyles}

        /* 强制覆盖容器背景色 - 必须在原始样式之后 */
        body, html {
            background: white !important;
        }
        .presentation-container {
            background: white !important;
        }
        .slide-page {
            background: white !important;
        }
    </style>
</head>
<body>
    <div class="navigation">
        <button class="nav-button" onclick="window.print()">打印/保存为PDF</button>
        <button class="nav-button" onclick="scrollToTop()">回到顶部</button>
        <button class="nav-button" onclick="toggleFullscreen()">全屏模式</button>
    </div>

    <div class="slide-counter">
        <span id="current-slide">1</span> / ${slideFiles.length}
    </div>

    <div class="slide-navigation">
        <button class="slide-nav-button" onclick="previousSlide()" id="prev-btn">上一页</button>
        <span id="slide-indicator">第 1 页</span>
        <button class="slide-nav-button" onclick="nextSlide()" id="next-btn">下一页</button>
    </div>

    <div class="presentation-container" id="presentation">
        ${slideContents}
    </div>

    <script>
        // 增强JavaScript执行函数
        function enhanceJavaScriptExecution() {
            console.log('Enhancing JavaScript execution in exported PPT...');

            // 确保所有canvas元素可见
            document.querySelectorAll('canvas').forEach(function(canvas) {
                canvas.style.display = 'block';
                canvas.style.visibility = 'visible';
                canvas.style.width = '100%';
                canvas.style.minHeight = '200px';
            });

            // 确保图表容器可见
            document.querySelectorAll('.chart-container').forEach(function(container) {
                container.style.display = 'block';
                container.style.visibility = 'visible';
                container.style.height = '450px';
                container.style.width = '100%';
                container.style.minHeight = '300px';
            });

            // 确保幻灯片元素可见
            document.querySelectorAll('.slide, [class*="slide-"]').forEach(function(slide) {
                slide.style.display = 'block';
                slide.style.overflow = 'visible';
                slide.style.height = 'auto';
                slide.style.minHeight = '600px';
                slide.style.width = '100%';
            });
        }

        // 在页面加载完成后执行增强功能
        window.addEventListener('DOMContentLoaded', function() {
            try {
                // 执行所有收集到的内联脚本
                // 执行内联脚本
try {
    ${Array.from(allScripts).join('\n')}
} catch (err) {
    console.error('Error executing inline scripts:', err);
}

                // 重新执行所有内联脚本以确保动态内容正确渲染
                document.querySelectorAll('script:not([src])').forEach(function(oldScript) {
                    try {
                        var newScript = document.createElement('script');
                        newScript.textContent = oldScript.textContent;
                        if (oldScript.parentNode) {
                            oldScript.parentNode.replaceChild(newScript, oldScript);
                        }
                    } catch (err) {
                        console.error('Error re-executing script:', err);
                    }
                });

                // 执行增强功能
                enhanceJavaScriptExecution();
            } catch (err) {
                console.error('Error in DOMContentLoaded handler:', err);
            }
        });

        let currentSlideIndex = 0;
        const totalSlides = ${slideFiles.length};

        // 更新幻灯片指示器
        function updateSlideIndicator() {
            document.getElementById('current-slide').textContent = currentSlideIndex + 1;
            document.getElementById('slide-indicator').textContent = \`第 \${currentSlideIndex + 1} 页\`;

            // 更新按钮状态
            document.getElementById('prev-btn').disabled = currentSlideIndex === 0;
            document.getElementById('next-btn').disabled = currentSlideIndex === totalSlides - 1;
        }

        // 滚动到指定幻灯片
        function scrollToSlide(index) {
            if (index >= 0 && index < totalSlides) {
                currentSlideIndex = index;
                const slide = document.getElementById(\`slide-\${index + 1}\`);
                if (slide) {
                    slide.scrollIntoView({ behavior: 'smooth', block: 'start' });
                }
                updateSlideIndicator();
            }
        }

        // 上一页
        function previousSlide() {
            if (currentSlideIndex > 0) {
                scrollToSlide(currentSlideIndex - 1);
            }
        }

        // 下一页
        function nextSlide() {
            if (currentSlideIndex < totalSlides - 1) {
                scrollToSlide(currentSlideIndex + 1);
            }
        }

        // 回到顶部
        function scrollToTop() {
            scrollToSlide(0);
        }

        // 全屏模式
        function toggleFullscreen() {
            if (!document.fullscreenElement) {
                document.documentElement.requestFullscreen();
            } else {
                document.exitFullscreen();
            }
        }

        // 键盘导航支持
        document.addEventListener('keydown', function(e) {
            switch(e.key) {
                case 'ArrowDown':
                case 'PageDown':
                case ' ': // 空格键
                    e.preventDefault();
                    nextSlide();
                    break;
                case 'ArrowUp':
                case 'PageUp':
                    e.preventDefault();
                    previousSlide();
                    break;
                case 'Home':
                    e.preventDefault();
                    scrollToSlide(0);
                    break;
                case 'End':
                    e.preventDefault();
                    scrollToSlide(totalSlides - 1);
                    break;
                case 'F11':
                    e.preventDefault();
                    toggleFullscreen();
                    break;
            }
        });

        // 鼠标滚轮支持
        let isScrolling = false;
        document.addEventListener('wheel', function(e) {
            if (isScrolling) return;

            isScrolling = true;
            setTimeout(() => { isScrolling = false; }, 300);

            if (e.deltaY > 0) {
                // 向下滚动
                nextSlide();
            } else {
                // 向上滚动
                previousSlide();
            }
        }, { passive: true });

        // 触摸支持（移动端）
        let touchStartY = 0;
        document.addEventListener('touchstart', function(e) {
            touchStartY = e.touches[0].clientY;
        });

        document.addEventListener('touchend', function(e) {
            const touchEndY = e.changedTouches[0].clientY;
            const diff = touchStartY - touchEndY;

            if (Math.abs(diff) > 50) { // 最小滑动距离
                if (diff > 0) {
                    nextSlide();
                } else {
                    previousSlide();
                }
            }
        });

        // 监听滚动事件，更新当前幻灯片指示器
        let scrollTimeout;
        window.addEventListener('scroll', function() {
            clearTimeout(scrollTimeout);
            scrollTimeout = setTimeout(() => {
                const slides = document.querySelectorAll('.slide-page');
                const viewportHeight = window.innerHeight;
                const scrollTop = window.scrollY;

                for (let i = 0; i < slides.length; i++) {
                    const slide = slides[i];
                    const rect = slide.getBoundingClientRect();

                    // 如果幻灯片在视口中心附近
                    if (rect.top <= viewportHeight / 2 && rect.bottom >= viewportHeight / 2) {
                        if (currentSlideIndex !== i) {
                            currentSlideIndex = i;
                            updateSlideIndicator();
                        }
                        break;
                    }
                }
            }, 100);
        });

        // 初始化
        updateSlideIndicator();
    </script>
</body>
</html>`;
};

// 生成专门用于PDF的HTML（基于PPT导出逻辑，但去掉导航元素）
export default ExportButton;
