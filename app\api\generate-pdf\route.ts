import { NextRequest, NextResponse } from 'next/server';
import puppeteerCore from 'puppeteer-core';
import { detectJSContentTypes, getSuggestedWaitTime } from '../../../lib/export-utils';

// 尝试导入完整版puppeteer（如果已安装）
let puppeteer: typeof puppeteerCore | null = null;
try {
  // 动态导入以避免在未安装puppeteer时出错
  puppeteer = require('puppeteer');
} catch (error) {
  console.log('未找到完整版puppeteer，将使用puppeteer-core');
  puppeteer = null;
}

// 检查是否有可用的Chrome浏览器
function getChromePath(): string | undefined {
  const fs = require('fs');
  const os = require('os');
  const platform = os.platform();
  let chromePaths: string[] = [];
  
  // 优先检查环境变量中的Chrome路径
  if (process.env.CHROME_PATH) {
    chromePaths.push(process.env.CHROME_PATH);
  }
  
  // 根据操作系统添加常见Chrome路径
  if (platform === 'win32') {
    // Windows常见Chrome路径
    chromePaths = [
      ...chromePaths,
      'C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe',
      'C:\\Program Files (x86)\\Google\\Chrome\\Application\\chrome.exe',
      'C:\\Users\\<USER>\\AppData\\Local\\Google\\Chrome\\Application\\chrome.exe',
      'C:\\Users\\<USER>\\AppData\\Local\\Google\\Chrome\\Application\\chrome.exe'
    ];
  } else if (platform === 'darwin') {
    // macOS常见Chrome路径
    chromePaths = [
      ...chromePaths,
      '/Applications/Google Chrome.app/Contents/MacOS/Google Chrome',
      '/Applications/Google Chrome Canary.app/Contents/MacOS/Google Chrome Canary',
      '/Applications/Chromium.app/Contents/MacOS/Chromium',
      `${os.homedir()}/Applications/Google Chrome.app/Contents/MacOS/Google Chrome`
    ];
  } else if (platform === 'linux') {
    // Linux常见Chrome路径
    chromePaths = [
      ...chromePaths,
      '/usr/bin/google-chrome',
      '/usr/bin/chromium',
      '/usr/bin/chromium-browser',
      '/snap/bin/chromium',
      '/usr/bin/google-chrome-stable'
    ];
  }

  // 尝试找到可用的Chrome路径
  for (const path of chromePaths) {
    if (path) {
      try {
        if (fs.existsSync(path)) {
          console.log(`找到Chrome浏览器路径: ${path}`);
          return path;
        }
      } catch (error) {
        // 继续尝试下一个路径
      }
    }
  }

  console.warn(`在${platform}平台上未找到Chrome浏览器路径`);
  return undefined;
}



export async function POST(request: NextRequest) {
  let browser = null;
  
  try {
    const body = await request.json();
    const { htmlContent, filename = 'generated.pdf', options = {} } = body;

    if (!htmlContent) {
      return NextResponse.json(
        { error: '缺少HTML内容' },
        { status: 400 }
      );
    }

    console.log('开始生成PDF，文件名:', filename);

    // 获取Chrome路径
    const chromePath = getChromePath();
    let launchOptions: any = {
      headless: true,
      args: [
        '--no-sandbox',
        '--disable-setuid-sandbox',
        '--disable-dev-shm-usage',
        '--disable-accelerated-2d-canvas',
        '--no-first-run',
        '--no-zygote',
        '--single-process',
        '--disable-gpu'
      ]
    };
    
    if (chromePath) {
      console.log('使用Chrome路径:', chromePath);
      launchOptions.executablePath = chromePath;
    } else {
      console.log('未找到本地Chrome浏览器，尝试使用Puppeteer内置下载功能');
      // 当未找到本地Chrome时，使用Puppeteer的自动下载功能
      if (puppeteer) {
        try {
          // 使用完整版puppeteer的自动下载功能
          browser = await puppeteer.launch(launchOptions);
          console.log('成功使用Puppeteer内置Chrome');
        } catch (error) {
          console.error('无法启动Puppeteer内置Chrome:', error);
          return NextResponse.json(
            { error: `无法启动Puppeteer内置Chrome: ${error instanceof Error ? error.message : String(error)}` },
            { status: 500 }
          );
        }
      } else {
        return NextResponse.json(
          { error: '未找到Chrome浏览器，请确保已安装Chrome或设置CHROME_PATH环境变量，或安装puppeteer包' },
          { status: 500 }
        );
      }
    }
    
    // 如果使用本地Chrome，则启动浏览器
    if (!browser && chromePath) {
      try {
        // 使用puppeteer-core启动浏览器
        browser = await puppeteerCore.launch(launchOptions);
      } catch (error) {
        console.error('启动浏览器失败:', error);
        return NextResponse.json(
          { error: `启动Chrome浏览器失败: ${error instanceof Error ? error.message : String(error)}` },
          { status: 500 }
        );
      }
    }

    console.log('浏览器启动成功');

    // 确保浏览器实例已创建
    if (!browser) {
      return NextResponse.json(
        { error: '浏览器实例创建失败' },
        { status: 500 }
      );
    }

    const page = await browser.newPage();

    // 设置视口大小为原始幻灯片尺寸
    await page.setViewport({
      width: 1280,
      height: 720,
      deviceScaleFactor: 1
    });

    // 设置HTML内容
    await page.setContent(htmlContent, {
      waitUntil: ['load', 'networkidle0', 'domcontentloaded'],
      timeout: 30000
    });

    // 注入样式，确保幻灯片尺寸和样式正确
    await page.addStyleTag({
      content: `
        @page {
          size: 1280px 720px;
          margin: 0;
        }
        html, body {
          margin: 0 !important;
          padding: 0 !important;
          width: 1280px !important;
          height: 720px !important;
          overflow: visible !important;
        }
        .slide-page {
          width: 1280px !important;
          height: 720px !important;
          margin: 0 !important;
          page-break-after: always !important;
          page-break-inside: avoid !important;
        }
        .slide {
          width: 1280px !important;
          height: 720px !important;
          margin: 0 !important;
          transform: none !important;
        }
        canvas {
          display: block !important;
          visibility: visible !important;
          width: 100% !important;
          min-height: 200px !important;
        }
        .chart-container {
          display: block !important;
          visibility: visible !important;
          height: 450px !important;
          width: 100% !important;
          min-height: 300px !important;
        }
      `
    });

    console.log('HTML内容设置完成，开始生成PDF');

    // 等待字体加载
    await page.evaluate(() => {
      return document.fonts.ready;
    });
    
    // 检测内容中的JavaScript类型
    const detectedContentTypes = detectJSContentTypes(htmlContent);
    if (detectedContentTypes.length > 0) {
      const contentTypeNames = detectedContentTypes.map(type => type.name).join(', ');
      console.log(`检测到JavaScript内容类型: ${contentTypeNames}`);
      
      // 注入通用脚本增强内容渲染
      await page.addScriptTag({
        content: `
          // 重新执行所有内联脚本
          document.querySelectorAll('script:not([src])').forEach(function(oldScript) {
            const newScript = document.createElement('script');
            newScript.textContent = oldScript.textContent;
            if (oldScript.parentNode) {
              oldScript.parentNode.replaceChild(newScript, oldScript);
            }
          });
          
          // 通知渲染完成
          window.jsContentRenderingComplete = false;
          setTimeout(() => {
            window.jsContentRenderingComplete = true;
            console.log('JavaScript内容渲染完成');
          }, ${getSuggestedWaitTime(htmlContent) || 2000});
        `
      });
      
      // 等待内容渲染完成
      const waitTime = getSuggestedWaitTime(htmlContent);
      console.log(`等待JavaScript内容渲染完成 (建议等待时间: ${waitTime}ms)...`);
      try {
        await page.waitForFunction('window.jsContentRenderingComplete === true', { timeout: Math.max(waitTime + 1000, 5000) });
        console.log('JavaScript内容渲染完成，继续生成PDF');
      } catch (error) {
        console.warn('等待JavaScript内容渲染超时，继续生成PDF:', error);
      }
    }

    // 使用screen媒体类型以保持网页样式
    await page.emulateMediaType('screen');

    // 生成PDF
    const defaultOptions = {
      width: '1280px',
      height: '720px',
      printBackground: true,
      margin: {
        top: '0px',
        right: '0px',
        bottom: '0px',
        left: '0px'
      },
      preferCSSPageSize: true,
      displayHeaderFooter: false
    };

    const pdfOptions = { ...defaultOptions, ...options };
    const pdfBuffer = await page.pdf(pdfOptions);

    console.log('PDF生成成功，大小:', pdfBuffer.length, 'bytes');

    // 返回PDF文件
    return new NextResponse(pdfBuffer, {
      status: 200,
      headers: {
        'Content-Type': 'application/pdf',
        'Content-Disposition': `attachment; filename="${filename}"`,
        'Content-Length': pdfBuffer.length.toString()
      }
    });

  } catch (error) {
    console.error('PDF生成错误:', error);
    
    return NextResponse.json(
      { 
        error: 'PDF生成失败', 
        details: error instanceof Error ? error.message : '未知错误'
      },
      { status: 500 }
    );
  } finally {
    // 确保浏览器被关闭
    if (browser) {
      try {
        await browser.close();
        console.log('浏览器已关闭');
      } catch (error) {
        console.error('关闭浏览器时出错:', error);
      }
    }
  }
}
