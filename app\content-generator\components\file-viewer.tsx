"use client";

import React, { useEffect, useState } from 'react';
import { GeneratedFile } from '../types';
import ContentViewerWrapper from '@/components/content-viewer/content-viewer-wrapper';

interface FileViewerProps {
  file: GeneratedFile;
  onViewModeChange: (viewMode: 'code' | 'preview' | 'split') => void;
  onVersionChange?: (fileId: string, versionIndex: number) => void;
  /**
   * 可选: 内容区自定义className（如 h-[600px] max-h-[80vh] 等）
   */
  contentClassName?: string;
}

const FileViewer: React.FC<FileViewerProps> = ({ file, onViewModeChange, onVersionChange, contentClassName }) => {
  // 缓存内容，避免频繁重新渲染
  const [cachedContent, setCachedContent] = useState(() => {
    if (file.versions && file.currentVersionIndex !== undefined && file.versions[file.currentVersionIndex]) {
      return file.versions[file.currentVersionIndex].content;
    }
    return file.content;
  });

  // 标记内容是否已编辑但未保存为新版本
  const [isContentEdited, setIsContentEdited] = useState(false);

  // 当文件内容或版本变化时更新缓存
  useEffect(() => {
    console.log('[FileViewer] 文件状态变化:', {
      fileName: file.name,
      currentVersionIndex: file.currentVersionIndex,
      versionsCount: file.versions?.length || 0,
      isModified: file.isModified,
      fileId: file.id
    });

    // 确保版本索引有效
    const versionIndex = file.currentVersionIndex ?? 0;

    // 获取当前版本的内容
    let nextContent = file.content;

    // 优先使用版本内容，如果版本存在的话
    if (file.versions && file.versions.length > 0) {
      // 确保版本索引在有效范围内
      const safeVersionIndex = Math.min(Math.max(versionIndex, 0), file.versions.length - 1);

      if (file.versions[safeVersionIndex]) {
        nextContent = file.versions[safeVersionIndex].content;
        console.log('[FileViewer] 使用版本内容:', {
          fileName: file.name,
          requestedVersionIndex: versionIndex,
          actualVersionIndex: safeVersionIndex,
          contentLength: nextContent.length,
          totalVersions: file.versions.length,
          versionDescription: file.versions[safeVersionIndex].taskDescription
        });
      } else {
        console.warn('[FileViewer] 版本索引无效，使用默认内容:', {
          fileName: file.name,
          versionIndex: safeVersionIndex,
          totalVersions: file.versions.length
        });
      }
    } else {
      console.log('[FileViewer] 无版本历史，使用默认内容:', {
        fileName: file.name,
        contentLength: nextContent.length
      });
    }

    // 更新缓存内容（强制更新，即使内容相同）
    console.log('[FileViewer] 更新缓存内容:', {
      fileName: file.name,
      contentLength: nextContent.length,
      versionIndex,
      totalVersions: file.versions?.length || 0,
      contentPreview: nextContent.substring(0, 100) + '...'
    });

    setCachedContent(nextContent);

    // 重置编辑状态，因为我们切换了版本
    setIsContentEdited(false);

    // 对幻灯片内容进行特殊处理，但不再使用强制重渲染
    // 这避免了切换时的闪烁问题
    if (nextContent.includes('<div class="slide"') || nextContent.includes('class="slide')) {
      console.log('[FileViewer] 检测到幻灯片内容，使用平滑切换');
      // 不再使用强制重渲染，而是依赖正常的状态更新
    }

  }, [file.content, file.name, file.currentVersionIndex, file.versions, file.isModified, file.id]);

  // 处理内容变更
  const handleContentChange = (newContent: string) => {
    console.log('[FileViewer] 内容已编辑:', {
      fileName: file.name,
      oldLength: cachedContent.length,
      newLength: newContent.length
    });

    // 更新缓存内容
    setCachedContent(newContent);

    // 标记内容已编辑
    setIsContentEdited(true);
  };

  // 保存编辑后的内容为新版本
  const saveAsNewVersion = () => {
    if (!isContentEdited) return;

    // 创建新版本
    const newVersion = {
      content: cachedContent,
      timestamp: Date.now(),
      taskDescription: '用户编辑'
    };

    // 准备新版本数组
    const updatedVersions = [...(file.versions || []), newVersion];

    // 更新版本索引为最新版本
    const newVersionIndex = updatedVersions.length - 1;

    // 调用版本变更回调
    if (onVersionChange) {
      console.log('[FileViewer] 保存为新版本:', {
        fileName: file.name,
        newVersionIndex,
        totalVersions: updatedVersions.length,
        versions: updatedVersions.map(v => v.taskDescription)
      });

      // 使用自定义事件来传递版本数组
      // 这比使用全局变量更安全
      const versionEvent = new CustomEvent('file-version-updated', {
        detail: {
          fileId: file.id,
          versions: updatedVersions,
          versionIndex: newVersionIndex
        },
        bubbles: true,
        cancelable: true
      });

      // 触发自定义事件
      document.dispatchEvent(versionEvent);

      // 重置编辑标记
      setIsContentEdited(false);

      // 注意：不再调用onVersionChange回调
      // 因为我们已经通过自定义事件传递了版本信息
      // 这避免了参数不匹配的问题
    }
  };

  // 处理下载文件
  const handleDownload = () => {
    // 如果内容已编辑但未保存，先保存为新版本
    if (isContentEdited) {
      saveAsNewVersion();
    }

    const blob = new Blob([cachedContent], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = file.name;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  // 获取文件图标
  const getFileIcon = () => {
    if (file.contentType === 'html') {
      return (
        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-orange-500" viewBox="0 0 20 20" fill="currentColor">
          <path fillRule="evenodd" d="M12.316 3.051a1 1 0 01.633 1.265l-4 12a1 1 0 11-1.898-.632l4-12a1 1 0 011.265-.633zM5.707 6.293a1 1 0 010 1.414L3.414 10l2.293 2.293a1 1 0 11-1.414 1.414l-3-3a1 1 0 010-1.414l3-3a1 1 0 011.414 0zm8.586 0a1 1 0 011.414 0l3 3a1 1 0 010 1.414l-3 3a1 1 0 11-1.414-1.414L16.586 10l-2.293-2.293a1 1 0 010-1.414z" clipRule="evenodd" />
        </svg>
      );
    } else {
      return (
        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-blue-500" viewBox="0 0 20 20" fill="currentColor">
          <path fillRule="evenodd" d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z" clipRule="evenodd" />
        </svg>
      );
    }
  };

  return (
    <div className="bg-white border border-gray-200 rounded-xl overflow-hidden shadow-sm hover:shadow-md transition-all duration-200">
      {/* 文件标题栏 */}
      <div className="bg-gradient-to-r from-gray-50 to-white border-b border-gray-200 p-4 flex justify-between items-center">
        <div className="flex items-center">
          {getFileIcon()}
          <h3 className="font-medium text-gray-800 ml-2">{file.name}</h3>

          {/* 文件状态指示器 */}
          <div className="flex ml-3 space-x-2">
            {file.isModified && (
              <span className="px-2 py-0.5 text-xs bg-yellow-100 text-yellow-800 rounded-full border border-yellow-200">已更新</span>
            )}
            {file.versions && (
              <span className="px-2 py-0.5 text-xs bg-blue-100 text-blue-800 rounded-full border border-blue-200">
                v{(file.currentVersionIndex !== undefined ? file.currentVersionIndex + 1 : 1)}/{file.versions.length}
              </span>
            )}
            <span className={`px-2 py-0.5 text-xs rounded-full ${
              file.contentType === 'html'
                ? 'bg-orange-100 text-orange-800 border border-orange-200'
                : 'bg-blue-100 text-blue-800 border border-blue-200'
            }`}>
              {file.contentType === 'html' ? 'HTML' : 'Markdown'}
            </span>
          </div>
        </div>

        {/* 文件操作按钮 */}
        {(file.status === 'completed' || (file.status === 'generating' && cachedContent)) && (
          <div className="flex space-x-3">
            {/* 版本切换按钮，只在有版本时显示 */}
            {file.versions && file.versions.length > 0 && (
              <div className="flex items-center bg-white rounded-lg border border-gray-200 shadow-sm">
                <button
                  className="px-2.5 py-1.5 text-sm rounded-l-lg bg-white text-gray-700 hover:bg-gray-50 transition-colors disabled:opacity-50 border-r border-gray-200"
                  onClick={() => {
                    // 切换到上一个版本
                    const currentIndex = file.currentVersionIndex ?? 0;
                    if (currentIndex > 0 && onVersionChange) {
                      console.log('[FileViewer] 切换到上个版本:', {
                        fileId: file.id,
                        fileName: file.name,
                        currentIndex,
                        nextIndex: currentIndex - 1,
                        totalVersions: file.versions?.length
                      });
                      
                      // 先调用父组件的版本变更回调，避免多次更新导致的闪烁
                      onVersionChange(file.id, currentIndex - 1);
                      
                      // 静默触发版本更新事件，不再强制更新内容
                      const versionEvent = new CustomEvent('file-version-updated', {
                        detail: {
                          fileId: file.id,
                          versions: file.versions || [],
                          versionIndex: currentIndex - 1
                        },
                        bubbles: true,
                        cancelable: true
                      });
                      
                      // 延迟触发事件，确保状态更新
                      setTimeout(() => {
                        document.dispatchEvent(versionEvent);
                        console.log('[FileViewer] 触发版本更新事件 (上一版本):', {
                          fileId: file.id,
                          versionIndex: currentIndex - 1,
                          versionsCount: file.versions?.length || 0
                        });
                      }, 10);
                    }
                  }}
                  disabled={file.currentVersionIndex === undefined || file.currentVersionIndex <= 0}
                  title="上一个版本"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                  </svg>
                </button>
                <span className="px-2 text-xs text-gray-600">
                  {file.currentVersionIndex !== undefined ? file.currentVersionIndex + 1 : 1}/{file.versions.length}
                </span>
                <button
                  className="px-2.5 py-1.5 text-sm rounded-r-lg bg-white text-gray-700 hover:bg-gray-50 transition-colors disabled:opacity-50"
                  onClick={() => {
                    // 切换到下一个版本
                    const currentIndex = file.currentVersionIndex ?? 0;
                    if (file.versions && currentIndex < file.versions.length - 1 && onVersionChange) {
                      console.log('[FileViewer] 切换到下一个版本:', {
                        fileId: file.id,
                        fileName: file.name,
                        currentIndex,
                        nextIndex: currentIndex + 1,
                        totalVersions: file.versions.length
                      });
                      
                      // 先调用父组件的版本变更回调，避免多次更新导致的闪烁
                      onVersionChange(file.id, currentIndex + 1);
                      
                      // 静默触发版本更新事件，不再强制更新内容
                      const versionEvent = new CustomEvent('file-version-updated', {
                        detail: {
                          fileId: file.id,
                          versions: file.versions || [],
                          versionIndex: currentIndex + 1
                        },
                        bubbles: true,
                        cancelable: true
                      });
                      
                      // 延迟触发事件，确保状态更新
                      setTimeout(() => {
                        document.dispatchEvent(versionEvent);
                        console.log('[FileViewer] 触发版本更新事件 (下一版本):', {
                          fileId: file.id,
                          versionIndex: currentIndex + 1,
                          versionsCount: file.versions?.length || 0
                        });
                      }, 10);
                    }
                  }}
                  disabled={file.currentVersionIndex === undefined || !file.versions || file.currentVersionIndex >= file.versions.length - 1}
                  title="下一个版本"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                  </svg>
                </button>
              </div>
            )}

            {/* 视图模式切换按钮 */}
            <div className="flex items-center bg-white rounded-lg border border-gray-200 shadow-sm">
              <button
                className={`px-2.5 py-1.5 text-sm rounded-l-lg transition-colors ${
                  file.viewMode === 'code'
                    ? 'bg-blue-500 text-white hover:bg-blue-600'
                    : 'bg-white text-gray-700 hover:bg-gray-50 border-r border-gray-200'
                }`}
                onClick={() => onViewModeChange('code')}
                title="代码视图"
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4" />
                </svg>
              </button>
              <button
                className={`px-2.5 py-1.5 text-sm border-r border-gray-200 transition-colors ${
                  file.viewMode === 'preview'
                    ? 'bg-blue-500 text-white hover:bg-blue-600'
                    : 'bg-white text-gray-700 hover:bg-gray-50'
                }`}
                onClick={() => onViewModeChange('preview')}
                title="预览视图"
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                </svg>
              </button>
              <button
                className={`px-2.5 py-1.5 text-sm rounded-r-lg transition-colors ${
                  file.viewMode === 'split'
                    ? 'bg-blue-500 text-white hover:bg-blue-600'
                    : 'bg-white text-gray-700 hover:bg-gray-50'
                }`}
                onClick={() => onViewModeChange('split')}
                title="分屏视图"
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 17V7m0 10a2 2 0 01-2 2H5a2 2 0 01-2-2V7a2 2 0 012-2h2a2 2 0 012 2m0 10a2 2 0 002 2h2a2 2 0 002-2M9 7a2 2 0 012-2h2a2 2 0 012 2m0 10V7m0 10a2 2 0 002 2h2a2 2 0 002-2V7a2 2 0 00-2-2h-2a2 2 0 00-2 2" />
                </svg>
              </button>
            </div>

            {/* 下载按钮 */}
            <button
              className="px-3 py-1.5 text-sm rounded-lg bg-gradient-to-r from-blue-500 to-blue-600 text-white hover:shadow-md transition-all flex items-center"
              onClick={handleDownload}
              title="下载文件"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
              </svg>
              下载
            </button>
          </div>
        )}
      </div>

      {/* 文件内容 */}
      <div className={contentClassName ? contentClassName : "h-[800px] max-h-[85vh]"} style={{
        minHeight: file.content && (file.content.includes('<div class="slide"') || file.content.includes('class="slide')) ? '800px' : '600px',
        height: '800px', // 增加高度，确保内容有足够的显示空间
        overflow: 'hidden' // 防止外层出现滚动条
      }}>
        {file.status === 'waiting' && (
          <div className="flex items-center justify-center h-full bg-gray-50 text-gray-500">
            <div className="text-center">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12 mx-auto text-gray-300 mb-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              <p className="text-gray-600">等待生成...</p>
            </div>
          </div>
        )}

        {file.status === 'generating' && !cachedContent && (
          <div className="flex items-center justify-center h-full bg-gray-50">
            <div className="text-center">
              <div className="flex justify-center space-x-2 mb-4">
                {[0, 1, 2, 3, 4].map((i) => (
                  <div
                    key={i}
                    className="w-3 h-3 bg-blue-500 rounded-full animate-bounce"
                    style={{
                      animationDelay: `${i * 0.1}s`,
                      animationDuration: '1s'
                    }}
                  ></div>
                ))}
              </div>
              <p className="text-gray-600">正在生成内容...</p>
            </div>
          </div>
        )}

        {/* 当有内容时显示内容查看器，即使还在生成中 */}
        {((file.status === 'completed' || file.status === 'generating') && cachedContent) && (
          <div className="relative h-full w-full" style={{
            minHeight: file.content && (file.content.includes('<div class="slide"') || file.content.includes('class="slide')) ? '600px' : '400px',
            height: '100%' // 确保占满整个父容器
          }}>
            {file.status === 'generating' && (
              <div className="absolute top-3 right-3 z-10 bg-blue-100 text-blue-800 text-xs px-3 py-1 rounded-full animate-pulse flex items-center shadow-sm">
                <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-blue-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                生成中...
              </div>
            )}
            <ContentViewerWrapper
              content={cachedContent}
              contentType={file.contentType}
              initialViewMode={file.viewMode}
              onViewModeChange={onViewModeChange}
              onContentChange={handleContentChange}
              editable={true}
              splitRatio={70} // 调整分屏比例，让代码区域更宽
            />

            {/* 如果内容已编辑但未保存，显示保存提示 */}
            {isContentEdited && (
              <div className="absolute bottom-3 right-3 z-10">
                <button
                  onClick={saveAsNewVersion}
                  className="px-3 py-1.5 bg-green-500 text-white rounded-lg hover:bg-green-600 shadow-md transition-all flex items-center"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                  </svg>
                  保存为新版本
                </button>
              </div>
            )}
          </div>
        )}

        {file.status === 'error' && (
          <div className="flex items-center justify-center h-full bg-red-50">
            <div className="text-center bg-white p-6 rounded-lg shadow-sm border border-red-100 max-w-md">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12 mx-auto text-red-500 mb-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              <h3 className="text-lg font-medium text-red-700 mb-2">生成失败</h3>
              <p className="text-red-600 mb-4">无法生成文件内容，请重试或修改您的需求</p>
              <button className="px-4 py-2 bg-red-100 hover:bg-red-200 rounded-lg text-red-700 transition-colors shadow-sm border border-red-200">
                重试
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default FileViewer;
